package controller

import (
	"net/http"
	"store-backend/pkg"
	"store-backend/pkg/models"
	"store-backend/pkg/models/dto"
	"store-backend/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetMe(action *gin.Context) {
	claims := action.MustGet("claims").(*pkg.Claims)
	action.JSON(http.StatusOK, gin.H{"signed": gin.H{"user": claims.Username, "expired": claims.ExpiresAt.Time}})
}

func GetPrivileges(action *gin.Context) {
	action.JSON(http.StatusOK, []string{string(models.Admin), string(models.SuperAdmin)})
}

func GetUsers(action *gin.Context) {
	var fetch, users []models.User
	ctx := action.Request.Context()
	cursor, err := models.GetUserCollection().Find(ctx, bson.M{})

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error fetching users"})
		return
	}

	defer cursor.Close(ctx)

	if err := cursor.All(ctx, &fetch); err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error parsing users"})
		return
	}

	for _, user := range fetch {
		users = append(users, models.User{
			ID:        user.ID,
			Name:      user.Name,
			Phone:     user.Phone,
			Address:   user.Address,
			Photo:     user.Photo,
			Username:  user.Username,
			Privilege: user.Privilege,
			Active:    user.Active,
		})
	}

	action.JSON(http.StatusOK, users)
}

func GetUser(action *gin.Context) {
	var query *bson.M
	var user models.User
	byParam := action.Param("id")
	userId, err := primitive.ObjectIDFromHex(byParam)
	ctx := action.Request.Context()

	if err != nil {
		query = &bson.M{"username": byParam}
	} else {
		query = &bson.M{"_id": userId}
	}

	err = models.GetUserCollection().FindOne(ctx, query).Decode(&user)

	if err != nil {
		action.JSON(http.StatusNotFound, gin.H{"error": utils.NotFoundError})
		return
	}

	action.JSON(http.StatusOK, user)
}

func CreateUser(action *gin.Context) {
	var payload dto.UserPayload
	ctx := action.Request.Context()

	if err := action.BindJSON(&payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": utils.InvalidPayloadError, "reasons": utils.ErrorFormat(err)})
		return
	}

	if err := validation.Struct(payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if payload.Password != "" {
		pkg.HashPassword(&payload.Password)
	}

	newbie := models.User{
		Name:      payload.Name,
		Phone:     payload.Phone,
		Address:   payload.Address,
		Photo:     payload.Photo,
		Privilege: payload.Privilege,
		Active:    payload.Active,
		Username:  payload.Username,
		Password:  payload.Password,
	}

	inserted, err := models.GetUserCollection().InsertOne(ctx, newbie)

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating user"})
		return
	}

	action.JSON(http.StatusCreated, gin.H{"message": "User created", "data": inserted})
}

func UpdateUser(action *gin.Context) {
	var query *bson.M
	var payload dto.UserExistPayload
	byParam := action.Param("id")
	userId, err := primitive.ObjectIDFromHex(byParam)
	ctx := action.Request.Context()

	if err != nil {
		query = &bson.M{"username": byParam}
	} else {
		query = &bson.M{"_id": userId}
	}

	if err := action.BindJSON(&payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": utils.InvalidPayloadError, "reasons": utils.ErrorFormat(err)})
		return
	}

	if err := validation.Struct(payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updated, err := models.GetUserCollection().UpdateOne(ctx, query, bson.M{"$set": payload})

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error updating user"})
		return
	}

	if updated.MatchedCount == 0 {
		action.JSON(http.StatusNotFound, gin.H{"error": utils.NotFoundError})
		return
	}

	action.JSON(http.StatusOK, gin.H{"message": "User updated"})
}

func ChangePasswordUser(action *gin.Context) {
	var query *bson.M
	var payload dto.UserPasswordPayload
	byParam := action.Param("id")
	userId, err := primitive.ObjectIDFromHex(byParam)
	ctx := action.Request.Context()

	if err != nil {
		query = &bson.M{"username": byParam}
	} else {
		query = &bson.M{"_id": userId}
	}

	if err := action.BindJSON(&payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": utils.InvalidPayloadError, "reasons": utils.ErrorFormat(err)})
		return
	}

	if err := validation.Struct(payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if payload.Password != "" {
		pkg.HashPassword(&payload.Password)
	}

	updated, err := models.GetUserCollection().UpdateOne(ctx, query, bson.M{"$set": payload})

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error updating user"})
		return
	}

	if updated.MatchedCount == 0 {
		action.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	action.JSON(http.StatusOK, gin.H{"message": "User: password updated"})
}
